import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import useVideoStorage from 'hooks/useVideoStorage'
import useOnlineStatus from 'hooks/useOnlineStatus'

import StorageInfo from 'components/pwa/StorageInfo'
import OfflineIndicator from 'components/pwa/OfflineIndicator'
import VideoPlayerSection from 'components/pwa/VideoPlayerSection'
import VideoGrid from 'components/pwa/VideoGrid'
import DeleteConfirmationModal from 'components/pwa/DeleteConfirmationModal'

export default function OfflineVideos({
  title = 'Offline Videos',
  subtitle = 'Watch videos offline',
}) {
  const { t } = useTranslation('pwa')
  const [selectedVideo, setSelectedVideo] = useState(null)
  const [showBackOnline, setShowBackOnline] = useState(false)
  const [wasOffline, setWasOffline] = useState(false)
  const [videoToDelete, setVideoToDelete] = useState(null)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [initializationError, setInitializationError] = useState(null)
  const isOnline = useOnlineStatus()

  const {
    videos,
    deleteVideo,
    storageUsage,
    loading: storageLoading,
    refresh,
    error: storageError,
  } = useVideoStorage(t)

  // Monitor for initialization errors
  useEffect(() => {
    if (storageError) {
      setInitializationError(`Storage initialization failed: ${storageError}`)
    } else {
      setInitializationError(null)
    }
  }, [storageError])

  // Handle the "back online" notification
  useEffect(() => {
    if (!isOnline) {
      setWasOffline(true)
      setShowBackOnline(false)
    } else if (wasOffline && isOnline) {
      // User just came back online
      setShowBackOnline(true)
      // Hide the "back online" message after 3 seconds
      const timer = setTimeout(() => {
        setShowBackOnline(false)
        setWasOffline(false)
      }, 3000)
      return () => clearTimeout(timer)
    } else if (isOnline && !wasOffline) {
      // Ensure we're in a clean state when online and never was offline
      setShowBackOnline(false)
    }
  }, [isOnline, wasOffline])

  // If there's an initialization error, show a simplified interface
  if (initializationError) {
    return (
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl md:text-4xl">
            {title}
          </h1>
          <p className="mt-2 text-base sm:text-lg text-gray-600">{subtitle}</p>
        </div>

        <div className="rounded-lg bg-yellow-50 border border-yellow-200 p-6">
          <div className="flex items-center mb-4">
            <svg
              className="w-6 h-6 text-yellow-500 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h2 className="text-lg font-semibold text-yellow-800">
              Storage Access Issue
            </h2>
          </div>
          <p className="text-yellow-700 mb-4">{initializationError}</p>
          <div className="space-y-3">
            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500"
            >
              Reload Page
            </button>
            <div className="text-sm text-yellow-600">
              <p>This might be due to:</p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>Browser compatibility issues with video storage</li>
                <li>Temporary storage access restrictions</li>
                <li>
                  Private browsing mode or browser settings blocking storage
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const handleVideoSelect = video => {
    setSelectedVideo(video)
  }

  const handleDeleteVideo = async videoId => {
    await deleteVideo(videoId)
    if (selectedVideo && selectedVideo.id === videoId) {
      setSelectedVideo(null)
    }
    // Close confirmation dialog
    setShowDeleteConfirmation(false)
    setVideoToDelete(null)
  }

  const handleDeleteClick = (e, video) => {
    e.stopPropagation()
    setVideoToDelete(video)
    setShowDeleteConfirmation(true)
  }

  const handleDeleteConfirm = () => {
    if (videoToDelete) {
      handleDeleteVideo(videoToDelete.id)
    }
  }

  const handleDeleteCancel = () => {
    setShowDeleteConfirmation(false)
    setVideoToDelete(null)
  }

  return (
    <div className="w-full space-y-4">
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl md:text-4xl">
              {title}
            </h1>
            <p className="mt-2 text-base sm:text-lg text-gray-600">
              {subtitle}
            </p>
          </div>

          {/* Compact Storage Info */}
          <StorageInfo
            storageUsage={storageUsage}
            videos={videos}
            refresh={refresh}
            storageLoading={storageLoading}
          />
        </div>

        {/* Offline indicator and back online notification */}
        <OfflineIndicator isOnline={isOnline} showBackOnline={showBackOnline} />
      </div>

      {/* Video Player Section */}
      <VideoPlayerSection selectedVideo={selectedVideo} />

      {/* Downloaded Videos Grid Section */}
      <VideoGrid
        videos={videos}
        selectedVideo={selectedVideo}
        onVideoSelect={handleVideoSelect}
        onDeleteClick={handleDeleteClick}
        refresh={refresh}
        storageLoading={storageLoading}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        show={showDeleteConfirmation}
        videoToDelete={videoToDelete}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
      />
    </div>
  )
}
