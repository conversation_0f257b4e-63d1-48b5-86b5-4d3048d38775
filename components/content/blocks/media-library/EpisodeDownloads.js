import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { usePageContext } from 'components/PageProvider'
import { formatBytes } from 'utils/strings'
import { getDocumentUrl } from 'utils/documents'
import { getImageUrl } from 'utils/images'
import useIsStandalone from 'hooks/useIsStandalone'

const LinkList = dynamic(() => import('ui/navigation/LinkList'))
const LinkItem = dynamic(() =>
  import('ui/navigation/LinkList').then(m => m.LinkItem)
)

const OfflineDownload = dynamic(() => import('components/pwa/OfflineDownload'))

export default function EpisodeDownloads({
  className = '',
  episodeDetail,
  episodeDownloads = [],
  title = 'Offline Video Downloads',
  displayMode = 'both',
  labelVideoHD = 'MP4 HD',
  labelVideoSD = 'MP4 SD',
  labelAudio = 'MP3',
} = {}) {
  const page = usePageContext()
  const isStandalone = useIsStandalone()

  const site = page?.site
  const mediaFormats = {
    'mp4-hd': labelVideoHD || 'MP4 HD',
    'mp4-sd': labelVideoSD || 'MP4 SD',
    'mp3': labelAudio || 'MP3',
    'audio': labelAudio || 'MP3',
  }
  const videoFormats = ['mp4-hd', 'mp4-sd']

  if (!episodeDownloads?.length) return null

  return site.pwa?.enabled &&
    site.pwa?.enableOfflineDownloads &&
    isStandalone &&
    ['both', 'offline'].includes(displayMode) ? (
    <div className={`w-full space-y-4 ${className}`}>
      <div className="border border-gray-200 rounded-lg p-4">
        <h3 className="text-xl mb-4">{title || 'Offline Video Downloads'}</h3>
        <OfflineDownload
          videoUrls={episodeDownloads
            .filter(download => videoFormats.includes(download.mediaFormat))
            .map(download => ({
              url: download.link,
              label: mediaFormats[download.mediaFormat],
              fileSize: download.fileSize,
            }))}
          thumbnailUrl={getImageUrl(episodeDetail.image)}
          title={episodeDetail.title}
          subtitle={episodeDetail.abstract}
          body={episodeDetail.body}
          channel={episodeDetail.channel?.title}
          show={episodeDetail.show?.title}
          videoId={episodeDetail.id}
        />
      </div>
    </div>
  ) : !isStandalone && ['both', 'direct'].includes(displayMode) ? (
    <div className={`w-full space-y-4 ${className}`}>
      <div className="border border-gray-200 rounded-lg p-4">
        <LinkList title={title}>
          {episodeDownloads.map((download, key) => {
            const { mediaFormat, link, fileSize, file } = download
            const url = file ? getDocumentUrl(file, site.entity) : link

            return (
              <LinkItem
                icon={
                  ['mp3', 'audio'].includes(mediaFormat) ? 'audio' : 'video'
                }
                key={key}
                label={mediaFormats[mediaFormat]}
                extra={formatBytes(fileSize, 0)}
                url={url}
              />
            )
          })}
        </LinkList>
      </div>
    </div>
  ) : null
}
EpisodeDownloads.propTypes = {
  className: PropTypes.string,
  episodeDetail: PropTypes.object,
  episodeDownloads: PropTypes.array,
  labelVideoHD: PropTypes.string,
  labelVideoSD: PropTypes.string,
  labelAudio: PropTypes.string,
}
